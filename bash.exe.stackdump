Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDEA370000 ntdll.dll
7FFDE8FB0000 KERNEL32.DLL
7FFDE7AD0000 KERNELBASE.dll
7FFDE9F60000 USER32.dll
7FFDE7640000 win32u.dll
7FFDEA300000 GDI32.dll
7FFDE7E90000 gdi32full.dll
7FFDE7900000 msvcp_win.dll
7FFDE7670000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDE8BA0000 advapi32.dll
7FFDE8E40000 msvcrt.dll
7FFDE9A30000 sechost.dll
7FFDE7A20000 bcrypt.dll
7FFDE9080000 RPCRT4.dll
7FFDE6DE0000 CRYPTBASE.DLL
7FFDE7A50000 bcryptPrimitives.dll
7FFDE8F70000 IMM32.DLL
